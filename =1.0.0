Collecting openai
  Downloading openai-1.92.2-py3-none-any.whl.metadata (29 kB)
Requirement already satisfied: anyio<5,>=3.5.0 in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from openai) (4.9.0)
Collecting distro<2,>=1.7.0 (from openai)
  Using cached distro-1.9.0-py3-none-any.whl.metadata (6.8 kB)
Requirement already satisfied: httpx<1,>=0.23.0 in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from openai) (0.28.1)
Collecting jiter<1,>=0.4.0 (from openai)
  Downloading jiter-0.10.0-cp313-cp313-macosx_11_0_arm64.whl.metadata (5.2 kB)
Requirement already satisfied: pydantic<3,>=1.9.0 in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from openai) (2.11.7)
Requirement already satisfied: sniffio in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from openai) (1.3.1)
Requirement already satisfied: tqdm>4 in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from openai) (4.67.1)
Requirement already satisfied: typing-extensions<5,>=4.11 in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from openai) (4.14.0)
Requirement already satisfied: idna>=2.8 in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai) (3.10)
Requirement already satisfied: certifi in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai) (2025.6.15)
Requirement already satisfied: httpcore==1.* in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai) (1.0.9)
Requirement already satisfied: h11>=0.16 in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai) (0.16.0)
Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/Downloads/agent/ai-agent/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai) (0.4.1)
Downloading openai-1.92.2-py3-none-any.whl (753 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 753.3/753.3 kB 2.9 MB/s eta 0:00:00
Using cached distro-1.9.0-py3-none-any.whl (20 kB)
Downloading jiter-0.10.0-cp313-cp313-macosx_11_0_arm64.whl (318 kB)
Installing collected packages: jiter, distro, openai

Successfully installed distro-1.9.0 jiter-0.10.0 openai-1.92.2
