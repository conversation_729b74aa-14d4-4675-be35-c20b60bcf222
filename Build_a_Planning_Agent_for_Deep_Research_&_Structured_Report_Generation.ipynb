{"cells": [{"cell_type": "markdown", "id": "f4169bfb-769a-4db3-833e-c827f19024b2", "metadata": {"id": "f4169bfb-769a-4db3-833e-c827f19024b2"}, "source": ["# Build a Planning Agent for Deep Research & Structured Report Generation with LangGraph\n", "\n", "### IMPORTANT: Be Careful this Agent will do a lot of searches so if you run it too many times you can easily exhaust your API limits for Tavily Search if you are on the free tier\n", "\n", "In this project we will be building a Planning Agent for Deep Research and Structured Report Generation in the form of Wiki-style Reports (structured with key sections and section headings)\n", "\n", "\n", "![](https://i.imgur.com/STSC73k.png)\n", "\n", "\n", "### Planning Agent for Deep Research and Structured Report Generation\n", "\n", "This project focuses on building a **Planning Agent for Deep Research and Structured Report Generation**. The agent automates the process of analyzing a user-defined topic, performing web research, and generating a well-structured report. The workflow includes the following components:\n", "\n", "1. **Report Planning**:\n", "   - The agent analyzes the user-provided **topic** and **default report template** to create a custom plan for the report.\n", "   - Sections such as **Introduction**, **Key Sections**, and **Conclusion** are defined based on the topic.\n", "   - A **web search tool** is used to collect information required before deciding the main sections.\n", "\n", "2. **Parallel Execution for Research and Writing**:\n", "   - The agent uses **parallel execution** to efficiently perform:\n", "     - **Web Research**: Queries are generated for each section and executed via the web search tool to retrieve up-to-date information.\n", "     - **Section Writing**: The retrieved data is used to write content for each section, with the following process:\n", "       - The **Researcher** gathers relevant data from the web.\n", "       - The **Section Writer** uses the data to generate structured content for the assigned section.\n", "\n", "3. **Formatting Completed Sections**:\n", "   - Once all sections are written, they are formatted to ensure consistency and adherence to the report structure.\n", "\n", "4. **Introduction and Conclusion Writing**:\n", "   - After the main sections are completed and formatted:\n", "     - The **Introduction** and **Conclusion** are written based on the content of the remaining sections (in parallel)\n", "     - This process ensures that these sections align with the overall flow and insights of the report.\n", "\n", "5. **Final Compilation**:\n", "   - All completed sections are compiled together to generate the **final report**.\n", "   - The final output is a comprehensive and structured document in the style of wiki docs.\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "9hEI3WL328vZ", "metadata": {"id": "9hEI3WL328vZ"}, "source": ["## Install Google Gemini, LangGraph and LangChain dependencies"]}, {"cell_type": "code", "execution_count": null, "id": "618eab5c-4ef7-4273-8e0b-a9c847897ed7", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "618eab5c-4ef7-4273-8e0b-a9c847897ed7", "outputId": "1a5d91c7-f950-4224-a9ec-a04cbb87e93f"}, "outputs": [], "source": ["!pip install langchain==0.3.14\n", "!pip install langchain-google-genai>=2.1.5\n", "!pip install google-generativeai>=0.8.5\n", "!pip install langchain-community==0.3.14\n", "!pip install langgraph==0.2.64\n", "!pip install rich"]}, {"cell_type": "markdown", "id": "H9c37cLnSrbg", "metadata": {"id": "H9c37cLnSrbg"}, "source": ["## Enter Google Gemini API Key"]}, {"cell_type": "code", "execution_count": 2, "id": "cv3JzCEx_PAd", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cv3JzCEx_PAd", "outputId": "fb2a7058-9c1a-4ae2-ceda-5b12d193b2f5"}, "outputs": [], "source": ["from getpass import getpass\n", "\n", "GEMINI_API_KEY = getpass('Enter Google Gemini API Key: ')"]}, {"cell_type": "markdown", "id": "ucWRRI3QztL2", "metadata": {"id": "ucWRRI3QztL2"}, "source": ["## Enter Tavily Search API Key\n", "\n", "Get a free API key from [here](https://tavily.com/#api)"]}, {"cell_type": "code", "execution_count": 3, "id": "mK-1WLzOrJdb", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mK-1WLzOrJdb", "outputId": "8b103693-f7d9-45c4-9728-47ae91763132"}, "outputs": [], "source": ["TAVILY_API_KEY = getpass('Enter Tavily Search API Key: ')"]}, {"cell_type": "markdown", "id": "1T0s0um5Svfa", "metadata": {"id": "1T0s0um5Svfa"}, "source": ["## Setup Environment Variables"]}, {"cell_type": "code", "execution_count": 4, "id": "x1YSuHNF_lbh", "metadata": {"id": "x1YSuHNF_lbh"}, "outputs": [], "source": ["import os\n", "\n", "os.environ['GOOGLE_API_KEY'] = GEMINI_API_KEY\n", "os.environ['TAVILY_API_KEY'] = TAVILY_API_KEY"]}, {"cell_type": "markdown", "id": "1Anj_VT7b4Rt", "metadata": {"id": "1Anj_VT7b4Rt"}, "source": ["## Define Agent State Schema\n", "\n", "Each specific set of operations (nodes) will have their own schema as defined below. You can customize this further based on your own style of report generation"]}, {"cell_type": "code", "execution_count": 5, "id": "o7EnucYkRb6f", "metadata": {"id": "o7EnucYkRb6f"}, "outputs": [], "source": ["from typing_extensions import TypedDict\n", "from pydantic import BaseModel, Field\n", "import operator\n", "from typing import  Annotated, List, Optional, Literal\n", "\n", "class Section(BaseModel):\n", "    name: str = Field(\n", "        description=\"Name for a particular section of the report.\",\n", "    )\n", "    description: str = Field(\n", "        description=\"Brief overview of the main topics and concepts to be covered in this section.\",\n", "    )\n", "    research: bool = Field(\n", "        description=\"Whether to perform web search for this section of the report.\"\n", "    )\n", "    content: str = Field(\n", "        description=\"The content for this section.\"\n", "    )\n", "\n", "class Sections(BaseModel):\n", "    sections: List[Section] = Field(\n", "        description=\"All the Sections of the overall report.\",\n", "    )\n", "\n", "class SearchQuery(BaseModel):\n", "    search_query: str = Field(None, description=\"Query for web search.\")\n", "\n", "class Queries(BaseModel):\n", "    queries: List[SearchQuery] = Field(\n", "        description=\"List of web search queries.\",\n", "    )\n", "\n", "class ReportStateInput(TypedDict):\n", "    topic: str # Report topic\n", "\n", "class ReportStateOutput(TypedDict):\n", "    final_report: str # Final report\n", "\n", "class ReportState(TypedDict):\n", "    topic: str # Report topic\n", "    sections: list[Section] # List of report sections\n", "    completed_sections: Annotated[list, operator.add] # Send() API\n", "    report_sections_from_research: str # String of any completed sections from research to write final sections\n", "    final_report: str # Final report\n", "\n", "class SectionState(TypedDict):\n", "    section: Section # Report section\n", "    search_queries: list[SearchQuery] # List of search queries\n", "    source_str: str # String of formatted source content from web search\n", "    report_sections_from_research: str # String of any completed sections from research to write final sections\n", "    completed_sections: list[Section] # Final key we duplicate in outer state for Send() API\n", "\n", "class SectionOutputState(TypedDict):\n", "    completed_sections: list[Section] # Final key we duplicate in outer state for Send() API\n"]}, {"cell_type": "markdown", "id": "J8gh0PeLnoD8", "metadata": {"id": "J8gh0PeLnoD8"}, "source": ["## Utility Functions\n", "\n", "- __`run_search_queries(...)`__ : This will asynchronously run tavily search queries for specific list of queries and return back the search results. This is async so it is non blocking and can be executed in parallel."]}, {"cell_type": "code", "execution_count": 6, "id": "x90h-0URszgf", "metadata": {"id": "x90h-0URszgf"}, "outputs": [], "source": ["from langchain_community.utilities.tavily_search import TavilySearchAPIWrapper\n", "import asyncio\n", "from dataclasses import asdict, dataclass\n", "from typing import Dict, Any, List, Union\n", "\n", "\n", "# just to handle objects created from LLM reponses\n", "@dataclass\n", "class SearchQuery:\n", "    search_query: str\n", "\n", "    def to_dict(self) -> Dict[str, Any]:\n", "        return as<PERSON>(self)\n", "\n", "\n", "tavily_search = TavilySearchAPIWrapper()\n", "\n", "\n", "async def run_search_queries(\n", "    search_queries: List[Union[str, SearchQuery]],\n", "    num_results: int = 5,\n", "    include_raw_content: bool = False\n", ") -> List[Dict]:\n", "\n", "    search_tasks = []\n", "\n", "    for query in search_queries:\n", "        # Handle both string and SearchQuery objects\n", "        # Just in case LLM fails to generate queries as:\n", "        # class SearchQuery(BaseModel):\n", "        #     search_query: str\n", "        query_str = query.search_query if isinstance(query, SearchQuery) else str(query) # text query\n", "\n", "        try:\n", "            # get results from tavily asynchronously (in parallel) for each search query\n", "            search_tasks.append(\n", "                tavily_search.raw_results_async(\n", "                    query=query_str,\n", "                    max_results=num_results,\n", "                    search_depth='advanced',\n", "                    include_answer=False,\n", "                    include_raw_content=include_raw_content\n", "                )\n", "            )\n", "        except Exception as e:\n", "            print(f\"Error creating search task for query '{query_str}': {e}\")\n", "            continue\n", "\n", "    # Execute all searches concurrently and await results\n", "    try:\n", "        if not search_tasks:\n", "            return []\n", "        search_docs = await asyncio.gather(*search_tasks, return_exceptions=True)\n", "        # Filter out any exceptions from the results\n", "        valid_results = [\n", "            doc for doc in search_docs\n", "            if not isinstance(doc, Exception)\n", "        ]\n", "        return valid_results\n", "    except Exception as e:\n", "        print(f\"Error during search queries: {e}\")\n", "        return []"]}, {"cell_type": "markdown", "id": "Db-RKn5MCi47", "metadata": {"id": "Db-RKn5MCi47"}, "source": ["- __`format_search_query_results(...)`__ : This will extract the context from tavily search results, make sure content is not duplicated from same urls and format it to show the Source, URL, relevant content (and optionally raw content which can be truncated based on number of tokens)"]}, {"cell_type": "code", "execution_count": 7, "id": "E9YSh5pAxW5r", "metadata": {"id": "E9YSh5pAxW5r"}, "outputs": [], "source": ["from typing import List, Dict, Union, Any\n", "\n", "def format_search_query_results(\n", "    search_response: Union[Dict[str, Any], List[Any]],\n", "    max_tokens: int = 2000,\n", "    include_raw_content: bool = False\n", ") -> str:\n", "    # Simple token estimation: ~4 characters per token\n", "    def estimate_tokens(text: str) -> int:\n", "        return len(text) // 4\n", "    sources_list = []\n", "\n", "    # Handle different response formats\n", "    # if search results is a dict\n", "    if isinstance(search_response, dict):\n", "        if 'results' in search_response:\n", "            sources_list.extend(search_response['results'])\n", "        else:\n", "            sources_list.append(search_response)\n", "    # if search results is a list\n", "    elif isinstance(search_response, list):\n", "        for response in search_response:\n", "            if isinstance(response, dict):\n", "                if 'results' in response:\n", "                    sources_list.extend(response['results'])\n", "                else:\n", "                    sources_list.append(response)\n", "            elif isinstance(response, list):\n", "                sources_list.extend(response)\n", "\n", "    if not sources_list:\n", "        return \"No search results found.\"\n", "\n", "    # Deduplicate by URL and keep unique sources (website urls)\n", "    unique_sources = {}\n", "    for source in sources_list:\n", "        if isinstance(source, dict) and 'url' in source:\n", "            if source['url'] not in unique_sources:\n", "                unique_sources[source['url']] = source\n", "\n", "    # Format output\n", "    formatted_text = \"Content from web search:\\n\\n\"\n", "    for i, source in enumerate(unique_sources.values(), 1):\n", "        formatted_text += f\"Source {source.get('title', 'Untitled')}:\\n===\\n\"\n", "        formatted_text += f\"URL: {source['url']}\\n===\\n\"\n", "        formatted_text += f\"Most relevant content from source: {source.get('content', 'No content available')}\\n===\\n\"\n", "\n", "        if include_raw_content:\n", "            # truncate raw webpage content to a certain number of tokens to prevent exceeding LLM max token window\n", "            raw_content = source.get(\"raw_content\", \"\")\n", "            if raw_content:\n", "                current_tokens = estimate_tokens(raw_content)\n", "                if current_tokens > max_tokens:\n", "                    # Truncate by character count (approximate)\n", "                    char_limit = max_tokens * 4\n", "                    truncated_content = raw_content[:char_limit]\n", "                else:\n", "                    truncated_content = raw_content\n", "                formatted_text += f\"Raw Content: {truncated_content}\\n\\n\"\n", "\n", "    return formatted_text.strip()"]}, {"cell_type": "markdown", "id": "lM0iTr7iC-17", "metadata": {"id": "lM0iTr7iC-17"}, "source": ["## Test Sample Utility Functions"]}, {"cell_type": "code", "execution_count": null, "id": "60GtL630zXH4", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "60GtL630zXH4", "outputId": "829b617b-c306-4fe5-9cb8-db67c56ab497"}, "outputs": [], "source": ["docs = await run_search_queries(['langgraph'], include_raw_content=True)\n", "docs"]}, {"cell_type": "code", "execution_count": null, "id": "zFNI1k9bKmBP", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zFNI1k9bKmBP", "outputId": "8b59c514-2c61-436e-bd71-54d5d06ae8fc"}, "outputs": [], "source": ["docs[0]"]}, {"cell_type": "code", "execution_count": 10, "id": "naEyCquUxiFT", "metadata": {"id": "naEyCquUxiFT"}, "outputs": [], "source": ["output = format_search_query_results(docs, max_tokens=500, include_raw_content=True)"]}, {"cell_type": "code", "execution_count": null, "id": "qHJ7CU1ixoLI", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qHJ7CU1ixoLI", "outputId": "8535a4c1-bac0-425e-bd5b-c9d1a61f79c6"}, "outputs": [], "source": ["print(output)"]}, {"cell_type": "markdown", "id": "TVjGRN57M1Zp", "metadata": {"id": "TVjGRN57M1Zp"}, "source": ["## Default Report Template\n", "\n", "This is the starting point for the LLM to get an idea of how to build a general report and it will use this to build a custom report structure"]}, {"cell_type": "code", "execution_count": null, "id": "_Mub1ld70yih", "metadata": {"id": "_Mub1ld70yih"}, "outputs": [], "source": ["# Structure\n", "DEFAULT_REPORT_STRUCTURE = \"\"\"The report structure should focus on breaking-down the user-provided topic\n", "                              and building a comprehensive report in markdown using the following format:\n", "\n", "                              1. Introduction (no web search needed)\n", "                                    - Brief overview of the topic area\n", "\n", "                              2. Main Body Sections:\n", "                                    - Each section should focus on a sub-topic of the user-provided topic\n", "                                    - Include any key concepts and definitions\n", "                                    - Provide real-world examples or case studies where applicable\n", "\n", "                              3. Conclusion (no web search needed)\n", "                                    - Aim for 1 structural element (either a list of table) that distills the main body sections\n", "                                    - Provide a concise summary of the report\n", "\n", "                              When generating the final response in markdown, if there are special characters in the text,\n", "                              such as the dollar symbol, ensure they are escaped properly for correct rendering e.g $25.5 should become \\$25.5\n", "                          \"\"\""]}, {"cell_type": "markdown", "id": "w-R-V_1SM-Rn", "metadata": {"id": "w-R-V_1SM-Rn"}, "source": ["## Instruction Prompts for Report Planner\n", "\n", "There are two main instruction prompts:\n", "\n", "- __REPORT_PLAN_QUERY_GENERATOR_PROMPT:__ Helps the LLM to generate an initial list of questions based on the topic to get more information from the web about that topic so that it can plan the overall sections and structure of the report\n", "\n", "- __REPORT_PLAN_SECTION_GENERATOR_PROMPT:__ Here we feed the LLM with the default report template, the topic name and the search results from the intial queries generated to create a detailed structure for the report. The LLM will generate a structured response of the following fields for each major section which will be in the report (this is just the report structure - no content is created at this step):\n", "    - Name - Name for this section of the report.\n", "    - Description - Brief overview of the main topics and concepts to be covered in this section.\n", "    - Research - Whether to perform web search for this section of the report or not.\n", "    - Content - The content of the section, which you will leave blank for now."]}, {"cell_type": "code", "execution_count": 13, "id": "vjoRG-IP0zkx", "metadata": {"id": "vjoRG-IP0zkx"}, "outputs": [], "source": ["REPORT_PLAN_QUERY_GENERATOR_PROMPT = \"\"\"You are an expert technical report writer, helping to plan a report.\n", "\n", "The report will be focused on the following topic:\n", "{topic}\n", "\n", "The report structure will follow these guidelines:\n", "{report_organization}\n", "\n", "Your goal is to generate {number_of_queries} search queries that will help gather comprehensive information for planning the report sections.\n", "\n", "The query should:\n", "1. Be related to the topic\n", "2. Help satisfy the requirements specified in the report organization\n", "\n", "Make the query specific enough to find high-quality, relevant sources while covering the depth and breadth needed for the report structure.\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 14, "id": "qBjAzBjd2pYa", "metadata": {"id": "qBjAzBjd2pYa"}, "outputs": [], "source": ["REPORT_PLAN_SECTION_GENERATOR_PROMPT = \"\"\"You are an expert technical report writer, helping to plan a report.\n", "\n", "Your goal is to generate the outline of the sections of the report.\n", "\n", "The overall topic of the report is:\n", "{topic}\n", "\n", "The report should follow this organizational structure:\n", "{report_organization}\n", "\n", "You should reflect on this additional context information from web searches to plan the main sections of the report:\n", "{search_context}\n", "\n", "Now, generate the sections of the report. Each section should have the following fields:\n", "- Name - Name for this section of the report.\n", "- Description - Brief overview of the main topics and concepts to be covered in this section.\n", "- Research - Whether to perform web search for this section of the report or not.\n", "- Content - The content of the section, which you will leave blank for now.\n", "\n", "Consider which sections require web search.\n", "For example, introduction and conclusion will not require research because they will distill information from other parts of the report.\n", "\"\"\"\n"]}, {"cell_type": "markdown", "id": "BcGmRLcQOE-U", "metadata": {"id": "BcGmRLcQOE-U"}, "source": ["## Node Function for Report Planner\n", "\n", "![](https://i.imgur.com/54Jyv71.png)\n", "\n", "This function uses the two prompts created above to:\n", " - First generate some queries based on the user topic\n", " - Search the web and get some information on these queries\n", " - Use this information to generate the overall structure of the report with the key sections necessary to be created"]}, {"cell_type": "code", "execution_count": 15, "id": "OH8ihSnZ0hHf", "metadata": {"id": "OH8ihSnZ0hHf"}, "outputs": [], "source": ["from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "\n", "llm = ChatGoogleGenerativeAI(model=\"gemini-2.5-flash\", temperature=0.5)\n", "\n", "async def generate_report_plan(state: ReportState):\n", "    \"\"\"Generate the overall plan for building the report\"\"\"\n", "    topic = state[\"topic\"]\n", "    print('--- Generating Report Plan ---')\n", "\n", "    report_structure = DEFAULT_REPORT_STRUCTURE\n", "    number_of_queries = 8\n", "\n", "    structured_llm = llm.with_structured_output(Queries)\n", "\n", "    system_instructions_query = REPORT_PLAN_QUERY_GENERATOR_PROMPT.format(\n", "        topic=topic,\n", "        report_organization=report_structure,\n", "        number_of_queries=number_of_queries\n", "    )\n", "\n", "    try:\n", "        # Generate queries\n", "        results = structured_llm.invoke([\n", "            SystemMessage(content=system_instructions_query),\n", "            HumanMessage(content='Generate search queries that will help with planning the sections of the report.')\n", "        ])\n", "\n", "        # Convert SearchQuery objects to strings\n", "        query_list = [\n", "            query.search_query if isinstance(query, SearchQuery) else str(query)\n", "            for query in results.queries\n", "        ]\n", "\n", "        # Search web and ensure we wait for results\n", "        search_docs = await run_search_queries(\n", "            query_list,\n", "            num_results=5,\n", "            include_raw_content=False\n", "        )\n", "\n", "        if not search_docs:\n", "            print(\"Warning: No search results returned\")\n", "            search_context = \"No search results available.\"\n", "        else:\n", "            search_context = format_search_query_results(\n", "                search_docs,\n", "                include_raw_content=False\n", "            )\n", "\n", "        # Generate sections\n", "        system_instructions_sections = REPORT_PLAN_SECTION_GENERATOR_PROMPT.format(\n", "            topic=topic,\n", "            report_organization=report_structure,\n", "            search_context=search_context\n", "        )\n", "\n", "        structured_llm = llm.with_structured_output(Sections)\n", "        report_sections = structured_llm.invoke([\n", "            SystemMessage(content=system_instructions_sections),\n", "            HumanMessage(content=\"Generate the sections of the report. Your response must include a 'sections' field containing a list of sections. Each section must have: name, description, plan, research, and content fields.\")\n", "        ])\n", "\n", "        print('--- Generating Report Plan Completed ---')\n", "        return {\"sections\": report_sections.sections}\n", "\n", "    except Exception as e:\n", "        print(f\"Error in generate_report_plan: {e}\")\n", "        return {\"sections\": []}"]}, {"cell_type": "markdown", "id": "j8zCsQwuQbeO", "metadata": {"id": "j8zCsQwuQbeO"}, "source": ["## Instruction Prompts for Section Builder - Query Generator\n", "\n", "There is one main instruction prompt:\n", "\n", "- __REPORT_SECTION_QUERY_GENERATOR_PROMPT:__ Helps the LLM to generate a comprehensive list of questions for the topic of that specific section which needs to be built"]}, {"cell_type": "code", "execution_count": 16, "id": "uxLrzsyY5Mdl", "metadata": {"id": "uxLrzsyY5Mdl"}, "outputs": [], "source": ["REPORT_SECTION_QUERY_GENERATOR_PROMPT = \"\"\"Your goal is to generate targeted web search queries that will gather comprehensive information for writing a technical report section.\n", "\n", "Topic for this section:\n", "{section_topic}\n", "\n", "When generating {number_of_queries} search queries, ensure that they:\n", "1. Cover different aspects of the topic (e.g., core features, real-world applications, technical architecture)\n", "2. Include specific technical terms related to the topic\n", "3. Target recent information by including year markers where relevant (e.g., \"2024\")\n", "4. Look for comparisons or differentiators from similar technologies/approaches\n", "5. Search for both official documentation and practical implementation examples\n", "\n", "Your queries should be:\n", "- Specific enough to avoid generic results\n", "- Technical enough to capture detailed implementation information\n", "- Diverse enough to cover all aspects of the section plan\n", "- Focused on authoritative sources (documentation, technical blogs, academic papers)\"\"\""]}, {"cell_type": "markdown", "id": "JP6AVB1YRqpG", "metadata": {"id": "JP6AVB1YRqpG"}, "source": ["## Node Function for Section Builder - Generate Queries (Query Generator)\n", "\n", "This uses the section topic and the instruction prompt above to generate some questions for researching on the web for getting useful information on the section topic"]}, {"cell_type": "code", "execution_count": 17, "id": "1tdPfB6m3taO", "metadata": {"id": "1tdPfB6m3taO"}, "outputs": [], "source": ["def generate_queries(state: SectionState):\n", "    \"\"\" Generate search queries for a specific report section \"\"\"\n", "\n", "\n", "\n", "    # Get state\n", "    section = state[\"section\"]\n", "    print('--- Generating Search Queries for Section: '+ section.name +' ---')\n", "\n", "    # Get configuration\n", "    number_of_queries = 5\n", "\n", "    # Generate queries\n", "    structured_llm = llm.with_structured_output(Queries)\n", "\n", "    # Format system instructions\n", "    system_instructions = REPORT_SECTION_QUERY_GENERATOR_PROMPT.format(section_topic=section.description,\n", "                                                                       number_of_queries=number_of_queries)\n", "\n", "    # Generate queries\n", "    user_instruction = \"Generate search queries on the provided topic.\"\n", "    search_queries = structured_llm.invoke([SystemMessage(content=system_instructions),\n", "                                     HumanMessage(content=user_instruction)])\n", "\n", "    print('--- Generating Search Queries for Section: '+ section.name +' Completed ---')\n", "\n", "    return {\"search_queries\": search_queries.queries}"]}, {"cell_type": "markdown", "id": "6G_8doZxR8QC", "metadata": {"id": "6G_8doZxR8QC"}, "source": ["## Node Function for Section Builder - Search Web\n", "\n", "Takes the queries generated by `generate_queries(...)`for a specific section, searches the web and formats the search results using the utility functions we defined earlier"]}, {"cell_type": "code", "execution_count": 18, "id": "Te1lHnkqRcBH", "metadata": {"id": "Te1lHnkqRcBH"}, "outputs": [], "source": ["async def search_web(state: SectionState):\n", "    \"\"\" Search the web for each query, then return a list of raw sources and a formatted string of sources.\"\"\"\n", "\n", "    # Get state\n", "    search_queries = state[\"search_queries\"]\n", "\n", "    print('--- Searching Web for Queries ---')\n", "\n", "    # Web search\n", "    query_list = [query.search_query for query in search_queries]\n", "    search_docs = await run_search_queries(search_queries, num_results=6, include_raw_content=True)\n", "\n", "    # Deduplicate and format sources\n", "    search_context = format_search_query_results(search_docs, max_tokens=4000, include_raw_content=True)\n", "\n", "    print('--- Searching Web for Queries Completed ---')\n", "\n", "    return {\"source_str\": search_context}\n"]}, {"cell_type": "markdown", "id": "5_ytIcDFUQMZ", "metadata": {"id": "5_ytIcDFUQMZ"}, "source": ["## Instruction Prompts for Section Builder - Section Writer\n", "\n", "There is one main instruction prompt:\n", "\n", "- __SECTION_WRITER_PROMPT:__ Constrains the LLM to generate and write the content for a specific section using certain guidelines on style, structure, length, approach and the documents obtained from the web earlier using the `search_web(...)` function are also sent."]}, {"cell_type": "code", "execution_count": null, "id": "Fc8VGgaK-UkT", "metadata": {"id": "Fc8VGgaK-UkT"}, "outputs": [], "source": ["SECTION_WRITER_PROMPT = \"\"\"You are an expert technical writer crafting one specific section of a technical report.\n", "\n", "Title for the section:\n", "{section_title}\n", "\n", "Topic for this section:\n", "{section_topic}\n", "\n", "Guidelines for writing:\n", "\n", "1. Technical Accuracy:\n", "- Include specific version numbers\n", "- Reference concrete metrics/benchmarks\n", "- Cite official documentation\n", "- Use technical terminology precisely\n", "\n", "2. Length and Style:\n", "- Strict 150-200 word limit\n", "- No marketing language\n", "- Technical focus\n", "- Write in simple, clear language do not use complex words unnecessarily\n", "- Start with your most important insight in **bold**\n", "- Use short paragraphs (2-3 sentences max)\n", "\n", "3. Structure:\n", "- Use ## for section title (Markdown format)\n", "- Only use ONE structural element IF it helps clarify your point:\n", "  * Either a focused table comparing 2-3 key items (using Markdown table syntax)\n", "  * Or a short list (3-5 items) using proper Markdown list syntax:\n", "    - Use `*` or `-` for unordered lists\n", "    - Use `1.` for ordered lists\n", "    - Ensure proper indentation and spacing\n", "- End with ### Sources that references the below source material formatted as:\n", "  * List each source with title, date, and URL\n", "  * Format: `- Title : URL`\n", "\n", "3. Writing Approach:\n", "- Include at least one specific example or case study if available\n", "- Use concrete details over general statements\n", "- Make every word count\n", "- No preamble prior to creating the section content\n", "- Focus on your single most important point\n", "\n", "4. Use this source material obtained from web searches to help write the section:\n", "{context}\n", "\n", "5. Quality Checks:\n", "- Format should be Markdown\n", "- Exactly 150-200 words (excluding title and sources)\n", "- Careful use of only ONE structural element (table or bullet list) and only if it helps clarify your point\n", "- One specific example / case study if available\n", "- Starts with bold insight\n", "- No preamble prior to creating the section content\n", "- Sources cited at end\n", "- If there are special characters in the text, such as the dollar symbol,\n", "  ensure they are escaped properly for correct rendering e.g $25.5 should become \\$25.5\n", "\"\"\""]}, {"cell_type": "markdown", "id": "5Vg97bh3USLp", "metadata": {"id": "5Vg97bh3USLp"}, "source": ["## Node Function for Section Builder - Write Section (Section Writer)\n", "\n", "Uses the SECTION_WRITER_PROMPT from above and feeds it with the section name, description and web search documents and passes it to an LLM to write the content for that section"]}, {"cell_type": "code", "execution_count": 20, "id": "mSgrxeeJ8I-O", "metadata": {"id": "mSgrxeeJ8I-O"}, "outputs": [], "source": ["def write_section(state: SectionState):\n", "    \"\"\" Write a section of the report \"\"\"\n", "\n", "    # Get state\n", "    section = state[\"section\"]\n", "    source_str = state[\"source_str\"]\n", "\n", "    print('--- Writing Section : '+ section.name +' ---')\n", "\n", "    # Format system instructions\n", "    system_instructions = SECTION_WRITER_PROMPT.format(section_title=section.name,\n", "                                                       section_topic=section.description,\n", "                                                       context=source_str)\n", "\n", "    # Generate section\n", "    user_instruction = \"Generate a report section based on the provided sources.\"\n", "    section_content = llm.invoke([SystemMessage(content=system_instructions),\n", "                                  HumanMessage(content=user_instruction)])\n", "\n", "    # Write content to the section object\n", "    section.content = section_content.content\n", "\n", "    print('--- Writing Section : '+ section.name +' Completed ---')\n", "\n", "    # Write the updated section to completed sections\n", "    return {\"completed_sections\": [section]}"]}, {"cell_type": "markdown", "id": "JIvVGOPwSNgQ", "metadata": {"id": "JIvVGOPwSNgQ"}, "source": ["## Create the Section Builder Sub-Agent\n", "\n", "![](https://i.imgur.com/5VEYGrQ.png)\n", "\n", "This agent (or to be more specific, sub-agent) will be called several times in parallel, once for each section to search the web, get content and then write up that specific section"]}, {"cell_type": "code", "execution_count": 21, "id": "UYB9crmZRcDD", "metadata": {"id": "UYB9crmZRcDD"}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "\n", "# Add nodes and edges\n", "section_builder = StateGraph(SectionState, output=SectionOutputState)\n", "section_builder.add_node(\"generate_queries\", generate_queries)\n", "section_builder.add_node(\"search_web\", search_web)\n", "section_builder.add_node(\"write_section\", write_section)\n", "\n", "section_builder.add_edge(START, \"generate_queries\")\n", "section_builder.add_edge(\"generate_queries\", \"search_web\")\n", "section_builder.add_edge(\"search_web\", \"write_section\")\n", "section_builder.add_edge(\"write_section\", END)\n", "section_builder_subagent = section_builder.compile()"]}, {"cell_type": "code", "execution_count": null, "id": "kkGDMBRTRcFx", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 449}, "id": "kkGDMBRTRcFx", "outputId": "c070cdba-dfef-40d5-cd45-00ab705662af"}, "outputs": [], "source": ["# Display the graph\n", "from IPython.display import display, Image\n", "Image(section_builder_subagent.get_graph().draw_mermaid_png())"]}, {"cell_type": "markdown", "id": "jrw1YPEHWD12", "metadata": {"id": "jrw1YPEHWD12"}, "source": ["### Create Dynamic Parallelization Node Function - Parallelize Section Writing\n", "\n", "`Send(...)` is used to parallelize and call the `section_builder_subagent` once for each section to write up the content (in parallel)"]}, {"cell_type": "code", "execution_count": 23, "id": "W7_5uWR2DkgN", "metadata": {"id": "W7_5uWR2DkgN"}, "outputs": [], "source": ["from langgraph.constants import Send\n", "\n", "def parallelize_section_writing(state: ReportState):\n", "    \"\"\" This is the \"map\" step when we kick off web research for some sections of the report in parallel and then write the section\"\"\"\n", "\n", "    # Kick off section writing in parallel via Send() API for any sections that require research\n", "    return [\n", "        Send(\"section_builder_with_web_search\", # name of the subagent node\n", "             {\"section\": s})\n", "            for s in state[\"sections\"]\n", "              if s.research\n", "    ]"]}, {"cell_type": "markdown", "id": "YDquAhSNYz06", "metadata": {"id": "YDquAhSNYz06"}, "source": ["## Create Format Sections Node Function\n", "\n", "This is basically the section where all the sections are formatted and combined together into one big document.\n", "\n", "![](https://i.imgur.com/6e5ZWK4.png)"]}, {"cell_type": "code", "execution_count": 24, "id": "uNlTvupUCHI9", "metadata": {"id": "uNlTvupUCHI9"}, "outputs": [], "source": ["def format_sections(sections: list[Section]) -> str:\n", "    \"\"\" Format a list of report sections into a single text string \"\"\"\n", "    formatted_str = \"\"\n", "    for idx, section in enumerate(sections, 1):\n", "        formatted_str += f\"\"\"\n", "{'='*60}\n", "Section {idx}: {section.name}\n", "{'='*60}\n", "Description:\n", "{section.description}\n", "Requires Research:\n", "{section.research}\n", "\n", "Content:\n", "{section.content if section.content else '[Not yet written]'}\n", "\n", "\"\"\"\n", "    return formatted_str\n", "\n", "\n", "def format_completed_sections(state: ReportState):\n", "    \"\"\" <PERSON><PERSON> completed sections from research and format them as context for writing the final sections \"\"\"\n", "\n", "    print('--- Formatting Completed Sections ---')\n", "\n", "    # List of completed sections\n", "    completed_sections = state[\"completed_sections\"]\n", "\n", "    # Format completed section to str to use as context for final sections\n", "    completed_report_sections = format_sections(completed_sections)\n", "\n", "    print('--- Formatting Completed Sections is Done ---')\n", "\n", "    return {\"report_sections_from_research\": completed_report_sections}\n"]}, {"cell_type": "markdown", "id": "l22EjIp1ZUTj", "metadata": {"id": "l22EjIp1ZUTj"}, "source": ["## Instruction Prompts for Final Section\n", "\n", "There is one main instruction prompt:\n", "\n", "- __FINAL_SECTION_WRITER_PROMPT:__ Constrains the LLM to generate and write the content for either the introduction OR conclusion using certain guidelines on style, structure, length, approach and the content of the already written sections are also sent."]}, {"cell_type": "code", "execution_count": null, "id": "OEZqj7PFA2U_", "metadata": {"id": "OEZqj7PFA2U_"}, "outputs": [], "source": ["FINAL_SECTION_WRITER_PROMPT = \"\"\"You are an expert technical writer crafting a section that synthesizes information from the rest of the report.\n", "\n", "Title for the section:\n", "{section_title}\n", "\n", "Topic for this section:\n", "{section_topic}\n", "\n", "Available report content of already completed sections:\n", "{context}\n", "\n", "1. Section-Specific Approach:\n", "\n", "For Introduction:\n", "- Use # for report title (Markdown format)\n", "- 50-100 word limit\n", "- Write in simple and clear language\n", "- Focus on the core motivation for the report in 1-2 paragraphs\n", "- Use a clear narrative arc to introduce the report\n", "- Include NO structural elements (no lists or tables)\n", "- No sources section needed\n", "\n", "For Conclusion/Summary:\n", "- Use ## for section title (Markdown format)\n", "- 100-150 word limit\n", "- For comparative reports:\n", "    * Must include a focused comparison table using Markdown table syntax\n", "    * Table should distill insights from the report\n", "    * Keep table entries clear and concise\n", "- For non-comparative reports:\n", "    * Only use ONE structural element IF it helps distill the points made in the report:\n", "    * Either a focused table comparing items present in the report (using Markdown table syntax)\n", "    * Or a short list using proper Markdown list syntax:\n", "      - Use `*` or `-` for unordered lists\n", "      - Use `1.` for ordered lists\n", "      - Ensure proper indentation and spacing\n", "- End with specific next steps or implications\n", "- No sources section needed\n", "\n", "3. Writing Approach:\n", "- Use concrete details over general statements\n", "- Make every word count\n", "- Focus on your single most important point\n", "\n", "4. Quality Checks:\n", "- For introduction: 50-100 word limit, # for report title, no structural elements, no sources section\n", "- For conclusion: 100-150 word limit, ## for section title, only ONE structural element at most, no sources section\n", "- Markdown format\n", "- Do not include word count or any preamble in your response\n", "- If there are special characters in the text, such as the dollar symbol,\n", "  ensure they are escaped properly for correct rendering e.g $25.5 should become \\$25.5\"\"\""]}, {"cell_type": "markdown", "id": "Myztb9Eeb-88", "metadata": {"id": "Myztb9Eeb-88"}, "source": ["## Create Write Final Sections Node Function\n", "\n", "This function uses the instruction prompot FINAL_SECTION_WRITER_PROMPT mentioned above to write up the introduction and conclusion. This function will be executed in parallel using `Send(...)` below\n", "\n", "![](https://i.imgur.com/pRv4PX8.png)"]}, {"cell_type": "code", "execution_count": 26, "id": "rRSoWr_MAIun", "metadata": {"id": "rRSoWr_MAIun"}, "outputs": [], "source": ["def write_final_sections(state: SectionState):\n", "    \"\"\" Write the final sections of the report, which do not require web search and use the completed sections as context\"\"\"\n", "\n", "    # Get state\n", "    section = state[\"section\"]\n", "    completed_report_sections = state[\"report_sections_from_research\"]\n", "\n", "    print('--- Writing Final Section: '+ section.name + ' ---')\n", "\n", "    # Format system instructions\n", "    system_instructions = FINAL_SECTION_WRITER_PROMPT.format(section_title=section.name,\n", "                                                             section_topic=section.description,\n", "                                                             context=completed_report_sections)\n", "\n", "    # Generate section\n", "    user_instruction = \"Craft a report section based on the provided sources.\"\n", "    section_content = llm.invoke([SystemMessage(content=system_instructions),\n", "                                  HumanMessage(content=user_instruction)])\n", "\n", "    # Write content to section\n", "    section.content = section_content.content\n", "\n", "    print('--- Writing Final Section: '+ section.name + ' Completed ---')\n", "\n", "    # Write the updated section to completed sections\n", "    return {\"completed_sections\": [section]}"]}, {"cell_type": "markdown", "id": "XIHk7dkbdGdn", "metadata": {"id": "XIHk7dkbdGdn"}, "source": ["### Create Dynamic Parallelization Node Function - Parallelize Final Section Writing\n", "\n", "`Send(...)` is used to parallelize and call the `write_final_sections` once for each of the introduction and conclusion to write up the content (in parallel)"]}, {"cell_type": "code", "execution_count": 27, "id": "gaXMkCuZDP9h", "metadata": {"id": "gaXMkCuZDP9h"}, "outputs": [], "source": ["from langgraph.constants import Send\n", "\n", "def parallelize_final_section_writing(state: ReportState):\n", "    \"\"\" Write any final sections using the Send API to parallelize the process \"\"\"\n", "\n", "    # Kick off section writing in parallel via Send() API for any sections that do not require research\n", "    return [\n", "        Send(\"write_final_sections\",\n", "             {\"section\": s, \"report_sections_from_research\": state[\"report_sections_from_research\"]})\n", "                 for s in state[\"sections\"]\n", "                    if not s.research\n", "    ]"]}, {"cell_type": "markdown", "id": "fCwKY0o_dWeM", "metadata": {"id": "fCwKY0o_dWeM"}, "source": ["## Compile Final Report Node Function\n", "\n", "This function combines all the sections of the report together and compiles it into the final report document\n", "\n", "![](https://i.imgur.com/wLxCNZ5.png)"]}, {"cell_type": "code", "execution_count": 28, "id": "PPlIQZl2Ddrk", "metadata": {"id": "PPlIQZl2Ddrk"}, "outputs": [], "source": ["def compile_final_report(state: ReportState):\n", "    \"\"\" Compile the final report \"\"\"\n", "\n", "    # Get sections\n", "    sections = state[\"sections\"]\n", "    completed_sections = {s.name: s.content for s in state[\"completed_sections\"]}\n", "\n", "    print('--- Compiling Final Report ---')\n", "\n", "    # Update sections with completed content while maintaining original order\n", "    for section in sections:\n", "        section.content = completed_sections[section.name]\n", "\n", "    # Compile final report\n", "    all_sections = \"\\n\\n\".join([s.content for s in sections])\n", "    # Escape unescaped $ symbols to display properly in Markdown\n", "    formatted_sections = all_sections.replace(\"\\\\$\", \"TEMP_PLACEHOLDER\")  # Temporarily mark already escaped $\n", "    formatted_sections = formatted_sections.replace(\"$\", \"\\\\$\")  # Escape all $\n", "    formatted_sections = formatted_sections.replace(\"TEMP_PLACEHOLDER\", \"\\\\$\")  # Restore originally escaped $\n", "\n", "# Now escaped_sections contains the properly escaped Markdown text\n", "\n", "\n", "    print('--- Compiling Final Report Done ---')\n", "\n", "    return {\"final_report\": formatted_sections}\n"]}, {"cell_type": "markdown", "id": "i10VLrxKePMo", "metadata": {"id": "i10VLrxKePMo"}, "source": ["## Build our Report Writer Planning Agent\n", "\n", "We now bring all the defined components and sub-agent together and build our planning agent\n", "\n", "![](https://i.imgur.com/STSC73k.png)"]}, {"cell_type": "code", "execution_count": 29, "id": "8WhUNRSJDLGX", "metadata": {"id": "8WhUNRSJDLGX"}, "outputs": [], "source": ["builder = StateGraph(ReportState, input=ReportStateInput, output=ReportStateOutput)\n", "\n", "builder.add_node(\"generate_report_plan\", generate_report_plan)\n", "builder.add_node(\"section_builder_with_web_search\", section_builder_subagent)\n", "builder.add_node(\"format_completed_sections\", format_completed_sections)\n", "builder.add_node(\"write_final_sections\", write_final_sections)\n", "builder.add_node(\"compile_final_report\", compile_final_report)\n", "\n", "builder.add_edge(START, \"generate_report_plan\")\n", "builder.add_conditional_edges(\"generate_report_plan\",\n", "                              parallelize_section_writing,\n", "                              [\"section_builder_with_web_search\"])\n", "builder.add_edge(\"section_builder_with_web_search\", \"format_completed_sections\")\n", "builder.add_conditional_edges(\"format_completed_sections\",\n", "                              parallelize_final_section_writing,\n", "                              [\"write_final_sections\"])\n", "builder.add_edge(\"write_final_sections\", \"compile_final_report\")\n", "builder.add_edge(\"compile_final_report\", END)\n", "\n", "reporter_agent = builder.compile()"]}, {"cell_type": "code", "execution_count": null, "id": "d0owAmm_j5I-", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 896}, "id": "d0owAmm_j5I-", "outputId": "ab374eaf-b9bd-46ab-ff2a-b52e063af8dc"}, "outputs": [], "source": ["display(Image(reporter_agent.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "LfIg-JDVearE", "metadata": {"id": "LfIg-JDVearE"}, "source": ["## Run and Test our Agent"]}, {"cell_type": "code", "execution_count": 31, "id": "ddra7VuHbiwn", "metadata": {"id": "ddra7VuHbiwn"}, "outputs": [], "source": ["from IPython.display import display\n", "from rich.console import Console\n", "from rich.markdown import Markdown as RichMarkdown\n", "\n", "async def call_planner_agent(agent, prompt, config={\"recursion_limit\": 50}, verbose=False):\n", "    events = agent.astream(\n", "        {'topic' : prompt},\n", "        config,\n", "        stream_mode=\"values\",\n", "    )\n", "\n", "    async for event in events:\n", "        for k, v in event.items():\n", "            if verbose:\n", "                if k != \"__end__\":\n", "                    display(RichMarkdown(repr(k) + ' -> ' + repr(v)))\n", "            if k == 'final_report':\n", "                print('='*50)\n", "                print('Final Report:')\n", "                md = RichMarkdown(v)\n", "                display(md)"]}, {"cell_type": "code", "execution_count": null, "id": "WlDHdzLpFPSO", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "WlDHdzLpFPSO", "outputId": "f6ea071c-3cac-4e8a-cece-8a94ecd8cb4b"}, "outputs": [], "source": ["topic = \"Detailed report on how to build Agentic AI systems, design patterns and current frameworks\"\n", "await call_planner_agent(agent=reporter_agent,\n", "                         prompt=topic)"]}, {"cell_type": "code", "execution_count": null, "id": "SvT_7UGgGiQA", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "SvT_7UGgGiQA", "outputId": "47702d82-f92a-4991-faec-486b25f07f9a"}, "outputs": [], "source": ["topic = \"Detailed report on how is NVIDIA winning the game against its competitors\"\n", "await call_planner_agent(agent=reporter_agent,\n", "                         prompt=topic)"]}, {"cell_type": "code", "execution_count": null, "id": "W1ng0JUSKai8", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "W1ng0JUSKai8", "outputId": "0e8d76c0-0f1e-48ba-e194-8670209be93a"}, "outputs": [], "source": ["topic = \"Detailed report on how DeepSeek has disrupted the AI Market\"\n", "await call_planner_agent(agent=reporter_agent,\n", "                         prompt=topic)"]}, {"cell_type": "code", "execution_count": null, "id": "611028a6", "metadata": {}, "outputs": [], "source": ["topic = \"Detailed report on analysis of recession in india in the it sector\"\n", "await call_planner_agent(agent=reporter_agent,\n", "                         prompt=topic)"]}, {"cell_type": "code", "execution_count": null, "id": "21b3ca2a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "ai-agent", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}