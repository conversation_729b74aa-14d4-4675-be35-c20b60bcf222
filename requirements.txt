# Google Gemini Dependencies (replacing OpenAI)
google-generativeai>=0.8.5
langchain-google-genai>=2.1.5

# LangChain Core Dependencies
langchain==0.3.14
langchain-community==0.3.14
langchain-core>=0.3.63
langchain-text-splitters>=0.3.8
langgraph==0.2.64

# Additional Dependencies
rich>=14.0.0
pydantic>=2.7.4
numpy>=1.26.2
aiohttp>=3.8.3
requests>=2.32.0
tenacity>=8.1.0
PyYAML>=5.3

# Jupyter Dependencies
jupyter>=1.1.1
ipykernel>=6.29.5
notebook>=7.4.3
jupyterlab>=4.4.3

# Optional: For token counting (if needed)
# Note: tiktoken is OpenAI-specific, consider alternatives for Gemini
# tiktoken  # Remove this line as it's OpenAI-specific
